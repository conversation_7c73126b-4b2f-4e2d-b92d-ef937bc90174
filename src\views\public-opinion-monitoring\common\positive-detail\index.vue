<template>
  <div class="page-container">
    <!-- 导航栏 -->
    <nav class="navbar">
      <a-button class="back-button" type="link" @click="goBack"> <span class="back-icon">←</span> 返回 </a-button>
    </nav>

    <!-- 反馈弹窗 -->
    <a-modal
      v-model:open="feedbackVisible"
      title="反馈意见"
      @ok="handleFeedbackSubmit"
      @cancel="handleFeedbackCancel"
      :maskClosable="false"
      :width="800"
      class="feedback-modal"
    >
      <div class="feedback-container">
        <a-form :model="feedbackForm" layout="vertical" class="feedback-form">
          <a-form-item label="反馈内容" name="content" :rules="[{ required: true, message: '请输入反馈内容' }]">
            <a-textarea
              v-model:value="feedbackForm.content"
              :rows="6"
              placeholder="请输入您的反馈意见，我们将认真听取您的建议..."
              :maxlength="500"
              show-count
              class="feedback-textarea"
            />
          </a-form-item>
        </a-form>
      </div>
      <template #footer>
        <div class="modal-footer">
          <a-button key="cancel" @click="handleFeedbackCancel" class="cancel-button">取消</a-button>
          <a-button key="submit" type="primary" @click="handleFeedbackSubmit" class="submit-button">提交反馈</a-button>
        </div>
      </template>
    </a-modal>

    <!-- 舆情渠道 -->
    <div class="opinion-source">
      <div class="chart-container">
        <div style="width: 50%; height: 400px">
          <nightingale-pie-chart :chart-data="pieChartData" width="100%" height="400px" />
        </div>
        <div style="width: 50%; height: 400px">
          <word-cloud-chart :chart-data="wordCloudData" width="100%" height="400px" />
        </div>
      </div>
    </div>

    <!-- 主内容区域：舆情内容表格 -->
    <div class="main-content">
      <div class="opinion-table-container">
        <!-- 添加筛选区域 -->
        <div class="filter-section">
          <div class="filter-row">
            <div class="filter-item time-filter">
              <label>检测时间：</label>
              <a-range-picker
                v-model:value="detectTime"
                :format="dateFormat"
                class="date-picker"
                :getCalendarContainer="(triggerNode) => triggerNode.parentNode"
                disabled
              />
            </div>
            <div class="filter-item source-filter">
              <label>信息来源渠道：</label>
              <a-select
                v-model:value="sourceChannel"
                mode="multiple"
                style="width: 100%"
                placeholder="选择信息来源渠道"
                allowClear
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                disabled
              >
                <a-select-option v-for="channel in channelList" :key="channel" :value="channel">{{ channel }}</a-select-option>
              </a-select>
            </div>
          </div>
          <div class="filter-row">
            <div class="filter-item keyword-filter">
              <label>言论内容关键词：</label>
              <div class="search-wrapper">
                <a-input v-model:value="keywordFilter" style="width: 100%" placeholder="请输入关键词，使用空格分隔，最多输入100个字符" allowClear />
              </div>
            </div>
          </div>
          <div class="filter-row">
            <div class="filter-item exclude-filter">
              <label>言论内容排除词：</label>
              <div class="search-wrapper">
                <a-input v-model:value="excludeFilter" style="width: 100%" placeholder="请输入排除词，使用空格分隔，最多输入100个字符" allowClear />
                <a-button type="primary" class="search-button" @click="filterTableData">查询</a-button>
              </div>
            </div>
          </div>
        </div>
        <div class="table-header">
          <h2>舆情内容</h2>
        </div>
        <!-- Ant Design Vue Table组件 -->
        <a-table
          :columns="columns"
          :data-source="filteredTableData"
          :pagination="{
            current: currentPage,
            pageSize: itemsPerPage,
            total: totalPage,
            pageSizeOptions: pageSizeOptions.map(String),
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条数据`,
          }"
          :loading="loading"
          @change="handleTableChange"
          bordered
          size="middle"
          :row-key="(record) => record.id || index"
          :class="'custom-table'"
        >
          <!-- 标题列自定义渲染 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'title'">
              <a :href="record.pageUrl" class="title-link" target="_blank" :title="record.title">
                <span class="link-text">{{ record.title }}</span>
                <i class="external-icon">↗</i>
              </a>
            </template>
            <!-- 情感类别列自定义渲染 -->
            <template v-if="column.dataIndex === 'sentiment'">
              <div class="sentiment-container">
                <span class="positive-sentiment">正面</span>
                <!-- <button class="feedback-button" @click="showFeedbackModal(record)"> -->
                  <!-- <img src="@/assets/icons/fankuiii.png" class="feedback-icon" alt="反馈" />  -->
                <!-- </button> -->
              </div>
            </template>
          </template>
          <!-- 空数据显示 -->
          <template #emptyText>
            <div class="empty-data-container">
              <i class="empty-icon">🔍</i>
              <p>没有找到匹配的正面舆情数据</p>
              <p class="empty-data-tip">请尝试调整筛选条件后再次查询</p>
            </div>
          </template>
        </a-table>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue';
  import dayjs from 'dayjs';
  import { useUserStore } from '/@/store/modules/user';
  import { getSentimentDetailListApi, getHotTodayDataApi, getCustomNameByPlanIdApi, getPublicOpinionDataSourceApi } from '@/api/public-opinion-monitoring/today';
  import { useRoute } from 'vue-router';
  import { message, TreeSelect } from 'ant-design-vue';
  import NightingalePieChart from '../../components/chart/NightingalePieChart.vue';
  import WordCloudChart from '../../components/chart/WordCloudChart.vue';

  const SHOW_PARENT = TreeSelect.SHOW_PARENT;

  const dateFormat = 'YYYY-MM-DD';
  const userStore = useUserStore();
  const userId = userStore.getUserInfo?.id;
  const route = useRoute();

  // 从路由参数获取planId，如果没有则从localStorage获取，如果还是没有则默认为空字符串
  const planId = ref(route.query.planId || localStorage.getItem('currentPlanId') || '');

  // 防抖函数
  const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };

  // 事件监听器引用，用于清理
  let storageListener = null;
  let planIdListener = null;

  // 添加事件监听器，监听localStorage的变化
  storageListener = (event) => {
    if (event.key === 'currentPlanId' && event.newValue !== planId.value) {
      planId.value = event.newValue || '';
    }
  };
  window.addEventListener('storage', storageListener);

  // 添加自定义事件监听，用于在同一窗口内监听planId变化
  planIdListener = (event) => {
    const newPlanId = event.detail;
    if (newPlanId !== planId.value) {
      planId.value = newPlanId || '';
    }
  };
  window.addEventListener('planIdChanged', planIdListener);

  // 渠道名称到ID的映射
  const channelNameToId = {
    百度贴吧: 1,
    微博: 2,
    知乎: 3,
  };



  // 表格数据
  const tableData = ref([]);
  const filteredTableData = ref([]);
  const loading = ref(false);

  // 初始化标志，防止初始化时重复调用
  const isInitialized = ref(false);

  // 筛选条件
  const detectTime = ref([]);
  const sourceChannel = ref([]);
  const keywordFilter = ref('');
  const excludeFilter = ref('');

  // 添加渠道列表
  const channelList = ref([]);

  // 分页相关
  const itemsPerPage = ref(10);
  const currentPage = ref(1);
  const totalPage = ref(0);
  const pageSizeOptions = [10, 20, 50, 100];

  // 定义表格列
  const columns = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      width: '20%',
      ellipsis: true,
    },
    {
      title: '言论内容',
      dataIndex: 'content',
      key: 'content',
      width: '35%',
      ellipsis: true,
    },
    {
      title: '信息来源渠道',
      dataIndex: 'channel_custom_name',
      key: 'channel_custom_name',
      width: '15%',
    },
    {
      title: '检测时间',
      dataIndex: 'postTime',
      key: 'postTime',
      width: '15%',
      sorter: (a, b) => {
        const dateA = a.postTime ? new Date(a.postTime).getTime() : 0;
        const dateB = b.postTime ? new Date(b.postTime).getTime() : 0;
        return dateA - dateB;
      },
      customRender: ({ text }) => {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '情感类别',
      dataIndex: 'sentiment',
      key: 'sentiment',
      width: '15%',
    },
  ];

  // 处理表格变化（分页、排序、筛选）
  const handleTableChange = (pagination) => {
    currentPage.value = pagination.current || 1;
    itemsPerPage.value = pagination.pageSize || 10;
    // 调用API获取新页的数据
    fetchSentimentDetailList();
  };

  const goBack = () => {
    window.history.back();
  };

  // 获取情感详情列表
  const fetchSentimentDetailList = async () => {
    try {
      loading.value = true;

      if (!planId.value || planId.value === '') {
        return;
      }

      //转换planId为数组
      let planIdList = [parseInt(planId.value)];

      // 如果没有选择渠道或选择了所有渠道，则一次性获取所有数据
      if (sourceChannel.value.length === 0 ||
          (channelList.value.length > 0 && sourceChannel.value.length === channelList.value.length)) {
        //参数
        const params = {
          planIds: planIdList,
          channelCustomName: undefined, // 不传渠道名称，表示获取所有渠道
          postTimeStart: detectTime.value && detectTime.value.length === 2 ? dayjs(detectTime.value[0]).valueOf() : undefined,
          postTimeEnd: detectTime.value && detectTime.value.length === 2 ? dayjs(detectTime.value[1]).valueOf() : undefined,
          // 处理关键词，使用空格分隔，作为并集关系（OR）传递给后端
          includeKeys: keywordFilter.value
            ? keywordFilter.value
                .split(/\s+/) // 只使用空格分割
                .map(item => item.trim()) // 去除每个关键词的前后空格
                .filter(Boolean) // 过滤空字符串
                .join(',') // 重新用逗号连接，作为OR关系传递
            : '',
          // 处理排除词，使用空格分隔，作为并集关系（OR）传递给后端
          excludeKeys: excludeFilter.value
            ? excludeFilter.value
                .split(/\s+/) // 只使用空格分割
                .map(item => item.trim()) // 去除每个排除词的前后空格
                .filter(Boolean) // 过滤空字符串
                .join(',') // 重新用逗号连接，作为OR关系传递
            : '',
          sentimentType: 'positive',
          pageSize: itemsPerPage.value,
          pageNum: currentPage.value,
        };

        //获取后端数据
        const response = await getSentimentDetailListApi(params);

        if (response) {
          // 处理数据字段映射，确保字段名称匹配
          const processedData = (response.detailList || []).map((item: any) => ({
            ...item,
            // 确保pageUrl字段存在，如果不存在则使用url字段
            pageUrl: item.pageUrl || item.url || '#',
            // 确保其他必要字段存在
            channel_custom_name: item.channel_custom_name || item.channelCustomName || item.source || '',
            postTime: item.postTime || item.time || item.createTime || '',
            sentiment: item.sentiment || item.sentimentType || '未知'
          }));

          filteredTableData.value = processedData;
          totalPage.value = response.totalCount || 0;
          currentPage.value = response.currentPage || 1;
        }
      } else {
        // 如果选择了特定渠道，则为每个渠道发送单独的请求
        let allDetailList = [];
        let totalCount = 0;

        // 为每个选中的渠道发送请求
        const requests = sourceChannel.value.map(channel => {
          const params = {
            planIds: planIdList,
            channelCustomName: channel,
            postTimeStart: detectTime.value && detectTime.value.length === 2 ? dayjs(detectTime.value[0]).valueOf() : undefined,
            postTimeEnd: detectTime.value && detectTime.value.length === 2 ? dayjs(detectTime.value[1]).valueOf() : undefined,
            // 处理关键词，使用空格分隔，作为并集关系（OR）传递给后端
            includeKeys: keywordFilter.value
              ? keywordFilter.value
                  .split(/\s+/) // 只使用空格分割
                  .map(item => item.trim()) // 去除每个关键词的前后空格
                  .filter(Boolean) // 过滤空字符串
                  .join(',') // 重新用逗号连接，作为OR关系传递
              : '',
            // 处理排除词，使用空格分隔，作为并集关系（OR）传递给后端
            excludeKeys: excludeFilter.value
              ? excludeFilter.value
                  .split(/\s+/) // 只使用空格分割
                  .map(item => item.trim()) // 去除每个排除词的前后空格
                  .filter(Boolean) // 过滤空字符串
                  .join(',') // 重新用逗号连接，作为OR关系传递
              : '',
            sentimentType: 'positive',
            pageSize: itemsPerPage.value,
            pageNum: currentPage.value,
          };

          return getSentimentDetailListApi(params);
        });

        // 等待所有请求完成
        const responses = await Promise.all(requests);

        // 合并结果
        responses.forEach(response => {
          if (response && response.detailList) {
            // 处理数据字段映射
            const processedData = response.detailList.map((item: any) => ({
              ...item,
              // 确保pageUrl字段存在，如果不存在则使用url字段
              pageUrl: item.pageUrl || item.url || '#',
              // 确保其他必要字段存在
              channel_custom_name: item.channel_custom_name || item.channelCustomName || item.source || '',
              postTime: item.postTime || item.time || item.createTime || '',
              sentiment: item.sentiment || item.sentimentType || '未知'
            }));

            allDetailList = [...allDetailList, ...processedData];
            totalCount += response.totalCount || 0;
          }
        });

        // 更新表格数据
        filteredTableData.value = allDetailList;
        totalPage.value = totalCount;
      }

      // 如果没有获取到数据
      if (!filteredTableData.value || filteredTableData.value.length === 0) {
        // console.warn('获取正面舆情列表返回空数据');
      }
    } catch (error) {
      // console.error('获取正面舆情列表出错:', error);
      if (error.response) {
        // console.error('错误响应:', error.response.data);
      }
    } finally {
      loading.value = false;
    }
  };

  // 根据筛选条件过滤数据
  const filterTableData = () => {
    // 重置分页参数
    currentPage.value = 1;
    // 调用API获取数据
    fetchSentimentDetailList();
  };

  // 饼图数据
  const pieChartData = ref([]);

  // 词云数据
  const wordCloudData = ref([]);

  // 获取热点数据（用于词云）
  const fetchHotTodayData = async () => {
    try {
      if (!planId.value || planId.value === '') {
        return;
      }

      // 获取当前时间的时间戳（毫秒）
      const now = new Date();
      const localDate = now.getTime();

      // 获取热点数据用于词云
      const hotParams = {
        planId: planId.value, // 使用从路由或localStorage获取的planId
        userId: userId,
        sentimentType: 'positive',
        localDate: localDate,
      };

      const hotResponse = await getHotTodayDataApi(hotParams);
      if (hotResponse && hotResponse.results && hotResponse.results.length > 0) {
        // console.log('热点API返回的results:', hotResponse.results);
        // 格式化为词云组件需要的数据格式
        wordCloudData.value = hotResponse.results.slice(0, 30).map((item) => ({
          title: item.token || '未知',
          views: item.tokenCount || 1,
        }));
      } else {
        // console.log('热点数据为空，使用默认值');
        wordCloudData.value = [
          { title: '暂无数据', views: 1 }
        ];
      }

      // 获取饼图数据（独立调用）
      fetchPieChartData();
    } catch (error) {
      // console.error('获取热点数据出错:', error);
      wordCloudData.value = [];
    }
  };

  // 获取饼图数据
  const fetchPieChartData = async () => {
    try {
      // 获取当前时间的时间戳（毫秒）
      const now = new Date();
      const localDate = now.getTime();

      const pieParams = {
        planId: parseInt(planId.value),
        userId: userId,
        sentimentType: 'positive',
        localDate: localDate,
      };

      // 调用饼图数据接口
      const pieResponse = await getPublicOpinionDataSourceApi(pieParams);
      if (pieResponse && pieResponse.source && pieResponse.source.length > 0) {
        // console.log('饼图API返回的source:', pieResponse.source);
        // 更新饼图数据
        pieChartData.value = pieResponse.source;
      } else {
        // console.log('饼图数据为空，使用默认值');
        pieChartData.value = [
          { name: '暂无数据', value: 100 }
        ];
      }
    } catch (error) {
      // console.error('获取饼图数据出错:', error);
      pieChartData.value = [
        { name: '暂无数据', value: 100 }
      ];
    }
  };



  // 防抖的API调用函数
  const debouncedFilterTableData = debounce(() => {
    filterTableData();
    fetchHotTodayData(); // 这个函数内部会调用 fetchPieChartData
  }, 300);

  const debouncedPlanIdChange = debounce(async (newVal) => {
    if (newVal) {
      // 清空筛选条件
      sourceChannel.value = [];
      // 重新获取渠道列表
      await fetchChannelList();
      // 重新获取舆情数据
      await fetchSentimentDetailList();
      // 重新获取热点数据和饼图数据
      await fetchHotTodayData(); // 这个函数内部会调用 fetchPieChartData

      // 标记初始化完成
      isInitialized.value = true;
    }
  }, 300);

  // 监听筛选条件变化
  watch(
    [detectTime, sourceChannel, keywordFilter, excludeFilter],
    () => {
      // 只有在初始化完成后才响应筛选条件变化
      if (isInitialized.value) {
        debouncedFilterTableData();
      }
    },
    { deep: true }
  );

  // 监听 planId 变化，重新获取渠道列表和舆情数据
  watch(planId, (newVal) => {
    debouncedPlanIdChange(newVal);
  }, { immediate: true }); // 添加 immediate: true 确保初始化时执行



  // 获取渠道列表
  const fetchChannelList = async () => {
    try {
      if (!planId.value || planId.value === '') {
        return;
      }

      const response = await getCustomNameByPlanIdApi({
        planId: parseInt(planId.value),
      });

      // 根据实际返回的数据结构进行处理
      if (response && response.results && Array.isArray(response.results)) {
        // 直接使用 response.results
        channelList.value = response.results;
        // 默认选择全部渠道
        sourceChannel.value = [...response.results];
        // console.log('获取到的渠道列表:', channelList.value);
      } else if (response && response.result && response.result.results && Array.isArray(response.result.results)) {
        // 兼容嵌套在 result.results 中的情况
        channelList.value = response.result.results;
        // 默认选择全部渠道
        sourceChannel.value = [...response.result.results];
        // console.log('获取到的渠道列表(嵌套):', channelList.value);
      } else {
        // console.error('获取渠道列表返回数据格式不支持:', response);
        channelList.value = [];
        sourceChannel.value = [];
      }
    } catch (error) {
      // console.error('获取渠道列表出错:', error);
      channelList.value = [];
    }
  };

  // 反馈相关
  const feedbackVisible = ref(false);
  const feedbackForm = ref({
    content: '',
    recordId: null
  });

  // 显示反馈弹窗
  const showFeedbackModal = (record) => {
    feedbackForm.value = {
      content: '',
      recordId: record.id
    };
    feedbackVisible.value = true;
  };

  // 提交反馈
  const handleFeedbackSubmit = async () => {
    try {
      // TODO: 调用提交反馈的API
      // console.log('提交反馈:', feedbackForm.value);
      // 提交成功后关闭弹窗
      feedbackVisible.value = false;
      // 清空表单
      feedbackForm.value = {
        content: '',
        recordId: null
      };
      // 显示成功提示
      message.success('反馈提交成功');
    } catch (error) {
      // console.error('提交反馈失败:', error);
      message.error('提交反馈失败，请重试');
    }
  };

  // 取消反馈
  const handleFeedbackCancel = () => {
    feedbackVisible.value = false;
    feedbackForm.value = {
      content: '',
      recordId: null
    };
  };

  onMounted(() => {
    // 设置默认时间范围为当天
    const today = dayjs();
    const startOfDay = today.startOf('day');
    const endOfDay = today.endOf('day');
    detectTime.value = [startOfDay, endOfDay];

    // 不在这里调用API，让 watch planId 的 immediate: true 来处理初始化
  });

  // 组件卸载时清理事件监听器
  onUnmounted(() => {
    if (storageListener) {
      window.removeEventListener('storage', storageListener);
    }
    if (planIdListener) {
      window.removeEventListener('planIdChanged', planIdListener);
    }
  });
</script>

<style scoped>
  /* 页面整体布局 */
  .page-container {
    font-family: Arial, sans-serif;
    width: 100%;
    padding: 20px;
    background-color: #f9f9f9;
  }

  /* 导航栏 */
  .navbar {
    background-color: #fff;
    padding: 10px 20px;
    border-bottom: 1px solid #eee;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }

  .back-button {
    background-color: transparent;
    border: none;
    color: #018ffb;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    padding: 5px 10px;
  }

  .back-button:hover {
    color: #0170c9;
    text-decoration: underline;
  }

  .back-icon {
    margin-right: 5px;
    font-size: 18px;
  }

  /* 舆情渠道 */
  .opinion-source {
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    min-height: 450px;
  }

  .chart-container {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    width: 100%;
    background-color: #fff;
    border-radius: 10px;
    padding: 10px;
  }

  #sourceChart,
  #wordCloudChart {
    flex: 1;
    width: 50%;
    height: 400px;
    background-color: transparent;
  }

  /* 主内容区域 */
  .main-content {
    margin-bottom: 20px;
  }

  /* 舆情内容表格容器 */
  .opinion-table-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* 表格头部 */
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .table-header h2 {
    font-size: 18px;
    font-weight: bold;
    margin: 0;
    color: #333;
    border-bottom: 4px solid #018ffb;
    width: 72px;
  }

  /* 添加筛选区域样式 */
  .filter-section {
    margin-bottom: 20px;
    padding: 24px;
    background-color: #fff;
    border-radius: 8px;
  }

  .filter-row {
    display: flex;
    gap: 32px;
    margin-bottom: 16px;
    align-items: center;
  }

  .filter-row:last-child {
    margin-bottom: 0;
  }

  .filter-item {
    display: flex;
    align-items: center;
  }

  .time-filter {
    flex: 0.8;
  }

  .source-filter {
    flex: 1.2;
  }

  .keyword-filter,
  .exclude-filter {
    flex: 1;
    width: 100%;
  }

  .filter-item label {
    min-width: 120px;
    color: #333;
    font-size: 15px;
    white-space: nowrap;
    font-weight: bold;
    margin-right: 12px;
  }

  .filter-input,
  .filter-select {
    flex: 1;
    padding: 10px 16px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    background-color: #fff;
    min-width: 180px;
    transition: all 0.3s;
  }

  .filter-input:hover,
  .filter-select:hover {
    border-color: #018ffb;
  }

  .filter-input:focus,
  .filter-select:focus {
    border-color: #018ffb;
    box-shadow: 0 0 0 2px rgba(1, 143, 251, 0.1);
    outline: none;
  }

  .search-wrapper {
    display: flex;
    gap: 12px;
    flex: 1;
  }

  .search-wrapper .filter-input {
    flex: 1;
  }

  .search-button {
    padding: 0 24px;
    height: 40px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 6px;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02);
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    min-width: 120px;
  }

  .search-button:hover {
    background-color: #1677ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
  }

  .search-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02);
  }

  /* 自定义表格样式 */
  .custom-table {
    width: 100%;
    font-size: 14px;
  }

  /* 表头样式 */
  :deep(.custom-table .ant-table-thead > tr > th) {
    background-color: #c2e8f8;
    color: #333;
    font-weight: bold;
    text-align: center;
    padding: 12px 16px;
  }

  /* 表格单元格样式 */
  :deep(.custom-table .ant-table-tbody > tr > td) {
    padding: 12px 16px;
    text-align: center;
    color: #666;
    vertical-align: middle;
    border-bottom: 1px solid #eee;
  }

  /* 斑马纹样式 */
  :deep(.custom-table .ant-table-tbody > tr:nth-child(odd)) {
    background-color: #ffffff;
  }

  :deep(.custom-table .ant-table-tbody > tr:nth-child(even)) {
    background-color: #dcf2fb;
  }

  /* 悬停样式 */
  :deep(.custom-table .ant-table-tbody > tr:hover > td) {
    background-color: #e6f7ff;
  }

  /* 表格列宽设置 */
  :deep(.custom-table .ant-table-column-title) {
    white-space: nowrap;
  }

  /* 内容列样式 */
  :deep(.custom-table .ant-table-cell-ellipsis) {
    white-space: normal;
    overflow: visible;
    max-height: 100px;
    overflow-y: auto;
    text-align: center;
  }

  /* 标题链接样式 */
  .title-link {
    color: #018ffb;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s;
    position: relative;
  }

  .title-link:hover {
    background-color: rgba(1, 143, 251, 0.1);
    color: #0170c9;
  }

  .link-text {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .external-icon {
    font-size: 12px;
    opacity: 0.7;
    transition: all 0.3s;
  }

  .title-link:hover .external-icon {
    opacity: 1;
    transform: translate(2px, -2px);
  }

  /* 来源列样式 */
  .source-column {
    text-align: center;
  }

  /* 时间列和情感类别列样式 */
  .time-column,
  .sentiment-column {
    text-align: center;
  }

  /* 表头样式 */
  .opinion-table th {
    background-color: #c2e8f8;
    font-weight: bold;
    color: #333;
    text-align: center;
    padding: 12px 16px;
    white-space: nowrap;
  }

  /* 情感类别样式 */
  .sentiment-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .positive-sentiment {
    color: #28a745;
    font-weight: bold;
    text-align: center;
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: rgba(40, 167, 69, 0.1);
  }

  .edit-button {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    color: #018ffb;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s;
  }

  .edit-button:hover {
    background-color: rgba(1, 143, 251, 0.1);
  }

  .edit-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
  }

  /* 反馈按钮样式 */
  .feedback-button {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    color: #018ffb;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s;
    margin-left: 8px;
  }

  .feedback-button:hover {
    background-color: rgba(1, 143, 251, 0.1);
  }

  .feedback-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
  }

  /* 分页样式 */
  .pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 15px 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .pagination-size {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .pagination-label {
    color: #666;
    font-size: 14px;
  }

  .page-size-select {
    padding: 6px 12px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: #fff;
    color: #333;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
  }

  .page-size-select:hover {
    border-color: #018ffb;
  }

  .pagination {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .pagination-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 16px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: #fff;
    color: #333;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
  }

  .pagination-btn:hover:not(:disabled) {
    background-color: #018ffb;
    color: #fff;
    border-color: #018ffb;
  }

  .pagination-btn:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
    color: #999;
    border-color: #e8e8e8;
  }

  .arrow-icon {
    font-size: 12px;
    line-height: 1;
  }

  .page-info {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background-color: #f5f5f5;
    border-radius: 4px;
    font-size: 14px;
  }

  .current-page {
    color: #018ffb;
    font-weight: 500;
  }

  .separator {
    color: #999;
  }

  .total-pages {
    color: #666;
  }

  .pagination-info {
    color: #666;
    font-size: 14px;
  }

  .total-count {
    color: #666;
    font-size: 14px;
  }

  /* 空数据提示样式 */
  .empty-data-message {
    padding: 40px 0;
    text-align: center;
  }

  .empty-data-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }

  .empty-icon {
    font-size: 32px;
    color: #ccc;
    margin-bottom: 10px;
  }

  .empty-data-container p {
    margin: 0;
    font-size: 16px;
    color: #666;
  }

  .empty-data-tip {
    font-size: 14px;
    color: #999;
  }

  /* 日期选择器样式 */
  .date-picker {
    width: 100%;
  }

  :deep(.ant-picker) {
    width: 100%;
    border-radius: 4px;
  }

  :deep(.ant-picker-input input) {
    color: #333;
    font-size: 14px;
  }

  :deep(.ant-picker:hover) {
    border-color: #018ffb;
  }

  :deep(.ant-picker-focused) {
    border-color: #018ffb;
    box-shadow: 0 0 0 2px rgba(1, 143, 251, 0.1);
  }

  /* 反馈弹窗样式 */
  :deep(.feedback-modal .ant-modal-content) {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  :deep(.feedback-modal .ant-modal-header) {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 16px 24px;
    margin-bottom: 0;
  }

  :deep(.feedback-modal .ant-modal-title) {
    color: #1a1a1a;
    font-size: 18px;
    font-weight: 600;
  }

  :deep(.feedback-modal .ant-modal-body) {
    padding: 24px;
    height: auto;
    max-height: 600px;
    overflow-y: auto;
    box-sizing: border-box;
  }

  .feedback-container {
    padding: 0 20px;
  }

  .feedback-form {
    width: 100%;
  }

  :deep(.feedback-modal .ant-form-item-label > label) {
    color: #1a1a1a;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  :deep(.feedback-modal .ant-form-item) {
    margin-bottom: 24px;
  }

  :deep(.feedback-modal .feedback-textarea) {
    width: 100%;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    transition: all 0.3s;
    font-size: 15px;
    padding: 16px;
    resize: none;
    background: #fafbfc;
    box-sizing: border-box;
    margin: 0 auto;
  }

  :deep(.feedback-modal .feedback-textarea:hover) {
    border-color: #018ffb;
  }

  :deep(.feedback-modal .feedback-textarea:focus) {
    border-color: #018ffb;
    box-shadow: 0 0 0 2px rgba(1, 143, 251, 0.08);
    background: #fff;
  }

  :deep(.feedback-modal .ant-input-textarea-show-count::after) {
    color: #8c8c8c;
    font-size: 12px;
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
  }

  .cancel-button {
    padding: 8px 24px;
    height: 40px;
    font-size: 14px;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    background: #fff;
    color: #666;
    transition: all 0.3s;
  }

  .cancel-button:hover {
    color: #40a9ff;
    border-color: #40a9ff;
    background: #fff;
  }

  .submit-button {
    padding: 8px 24px;
    height: 40px;
    font-size: 14px;
    border-radius: 6px;
    background: #018ffb;
    border: none;
    transition: all 0.3s;
  }

  .submit-button:hover {
    background: #1677ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  }

  .submit-button:active {
    transform: translateY(0);
    box-shadow: none;
  }
</style>
