<template>
  <div class="page-container" ref="page">
    
    <game-title @planUpdate="onPlanUpdate"/>

    <div class="select_card"  >
      <a-row style="padding-top: 0.8%; padding-bottom: 1%">
        <a-col :span="5">
          <a-space direction="vertical" style="margin-left: 8%">
            <a-range-picker 
            style="width: 15vw;height: 30px;" 
            v-model:value="dateRange" 
            :presets="rangePresets" 
            @change="onRangeChange"   
             :get-popup-container="getPopupContainer"/>
          </a-space>
        </a-col>
        <a-col :span="4" :offset="1">
          <a-select
            v-model:value="value"
            mode="multiple"
            label-in-value
            style="width: 500px"
            :options="treeDataOptions"
            placeholder="选择展示渠道"
            :get-popup-container="getPopupContainer"
            :max-tag-count="2"
            :max-tag-placeholder="maxTagPlaceholder"
          />
        
        </a-col>
      </a-row>
    </div>

    <div class="public-opinion">
      <div class="opinion-item">
        <div class="opinion-box">
            <!-- 左侧内容：图标和舆情总数 -->
            <div class="opinion-content">
              <img :src="logoUrl1" class="opinion-image" />
              <div class="opinion-text">
                <h3>舆情总数
                  <a-tooltip title="开始日期到结束日期的舆情总数">
                      <QuestionCircleOutlined style="color: #939699; margin-left: 5px" />
                  </a-tooltip>
                </h3>
                <p>{{ publicOpinionData.publicOpinionCount }}个</p>
              </div>
            </div>

            <!-- 右侧内容：日环比和周环比 -->
            <a-row :gutter="[30, 8]" type="flex" justify="space-between" class="opinion-metrics">
              <a-col :span="10" class="metric">
                <div class="DoD">
                  日环比
                  <a-tooltip title="相对于结束日期前一日的增长或减少百分比">
                    <QuestionCircleOutlined style="color: #939699; margin-left: 5px" />
                  </a-tooltip>
                </div>

                <div class="DoD_per">
                  <span>{{publicOpinionData.publicOpinionDayRatio}}</span>
                  <span>
                    <CaretUpOutlined v-if="publicOpinionData.publicOpinionDayFlag == '1'" style="color: #f5222d; margin-left: 5px;" />
                    <CaretDownOutlined v-else-if="publicOpinionData.publicOpinionDayFlag == '2'" style="color: #52c41a; margin-left: 5px;" />
                    <!-- <span v-else>{{ publicOpinionData.publicOpinionDayFlag }}</span> -->
                  </span>
                </div>  
              </a-col>
              
              <a-col :span="10" class="metric" >
                <div class="WoW">
                  周环比
                  <a-tooltip title="相对于结束日期前一周的增长或减少百分比">
                    <QuestionCircleOutlined style="color: #939699; margin-left: 5px" />
                  </a-tooltip>
                </div>

                <div class="WoW_per">
                  <span>{{ publicOpinionData.publicOpinionWeekRatio }}</span>
                  <span>
                    <CaretUpOutlined v-if="publicOpinionData.publicOpinionWeekFlag == '1'" style="color: #f5222d; margin-left: 5px;" />
                    <CaretDownOutlined v-else-if="publicOpinionData.publicOpinionWeekFlag == '2'" style="color: #52c41a; margin-left: 5px;" />
                    <!-- <span v-else>{{ publicOpinionData.publicOpinionDayFlag }}</span> -->
                  </span>
                </div>
                
              </a-col>
            </a-row>

            
        </div>
      </div>

      <div class="opinion-item">
        <div class="opinion-box">
            <!-- 左侧内容：图标和舆情总数 -->
            <div class="opinion-content">
              <img :src="logoUrl2" class="opinion-image" />
              <div class="opinion-text">
                <h3>发言人数
                  <a-tooltip title="开始日期到结束日期的发言人数">
                      <QuestionCircleOutlined style="color: #939699; margin-left: 5px" />
                  </a-tooltip>
                </h3>
                <p>{{ publicOpinionData.participantsNumber  }}个</p>
              </div>
            </div>

            <!-- 右侧内容：日环比和周环比 -->
            <a-row :gutter="[30, 8]" type="flex" justify="space-between" class="opinion-metrics">
              <a-col  :span="10" class="metric">
                <div class="DoD">
                  日环比
                  <a-tooltip title="相对于结束日期前一日的增长或减少百分比">
                    <QuestionCircleOutlined style="color: #939699; margin-left: 5px" />
                  </a-tooltip>
                </div>

                <div class="DoD_per">
                  <span>{{ publicOpinionData.participantsNumberDayRatio }}</span>
                  <span>
                    <CaretUpOutlined v-if="publicOpinionData.participantsNumberDayFlag == '1'" style="color: #f5222d; margin-left: 5px;" />
                    <CaretDownOutlined v-else-if="publicOpinionData.participantsNumberDayFlag == '2'" style="color: #52c41a; margin-left: 5px;" />
                    <!-- <span v-else>{{ publicOpinionData.participantsNumberDayFlag }}</span> -->
                  </span>
                </div>
              </a-col>

              <a-col  :span="10" class="metric" >
                <div class="WoW">
                  周环比
                  <a-tooltip title="相对于结束日期前一周的增长或减少百分比">
                    <QuestionCircleOutlined style="color: #939699; margin-left: 5px" />
                  </a-tooltip>
                </div>

                <div class="WoW_per">
                  <span>{{ publicOpinionData.participantsNumberWeekRatio }}</span>
                  <span>
                    <CaretUpOutlined v-if="publicOpinionData.participantsNumberWeekFlag == '1'" style="color: #f5222d; margin-left: 5px;" />
                    <CaretDownOutlined v-else-if="publicOpinionData.participantsNumberWeekFlag == '2'" style="color: #52c41a; margin-left: 5px;" />
                    <!-- <span v-else>{{ publicOpinionData.participantsNumberWeekFlag }}</span> -->
                  </span>
                </div>
              </a-col>
            </a-row>
        </div>
      </div>

      <div class="opinion-item">
        <div class="opinion-box">
            <!-- 左侧内容：图标和舆情总数 -->
            <div class="opinion-content">
              <img :src="logoUrl3" class="opinion-image" />
              <div class="opinion-text">
                <h3>正面舆情
                  <a-tooltip title="开始日期到结束日期的发言人数">
                      <QuestionCircleOutlined style="color: #939699; margin-left: 5px" />
                  </a-tooltip>
                </h3>
                <p>{{ publicOpinionData.sumPositive   }}个</p>
              </div>
            </div>

            <!-- 右侧内容：日环比和周环比 -->
            <a-row :gutter="[30, 8]" type="flex" justify="space-between" class="opinion-metrics">
              <a-col :span="10" class="metric">
                <div class="DoD">
                  日环比
                  <a-tooltip title="相对于结束日期前一日的增长或减少百分比">
                    <QuestionCircleOutlined style="color: #939699; margin-left: 5px" />
                  </a-tooltip>
                </div>

                <div class="DoD_per">
                  <span>{{ publicOpinionData.sumPositiveDayRatio}} </span>
                  <span>
                    <CaretUpOutlined v-if="publicOpinionData.sumNegativeDayFlag == '1'" style="color: #f5222d; margin-left: 5px;" />
                    <CaretDownOutlined v-else-if="publicOpinionData.sumNegativeDayFlag == '2'" style="color: #52c41a; margin-left: 5px;" />
                    <!-- <span v-else>{{ publicOpinionData.sumNegativeDayFlag }}</span> -->
                  </span>
                </div>
              </a-col>
              <a-col :span="10" class="metric">
                <div class="WoW">
                  周环比
                  <a-tooltip title="相对于结束日期前一周的增长或减少百分比">
                    <QuestionCircleOutlined style="color: #939699; margin-left: 5px" />
                  </a-tooltip>
                </div>

                <div class="WoW_per">
                  <span>{{ publicOpinionData.sumPositiveWeekRatio }}</span>
                  <span>
                    <CaretUpOutlined v-if="publicOpinionData.sumNegativeWeekFlag == '1'" style="color: #f5222d; margin-left: 5px;" />
                    <CaretDownOutlined v-else-if="publicOpinionData.sumNegativeWeekFlag == '2'" style="color: #52c41a; margin-left: 5px;" />
                    <!-- <span v-else>{{ publicOpinionData.sumNegativeWeekFlag }}</span> -->
                  </span>
                </div>
              </a-col>
            </a-row>

        </div>
      </div>

      <div class="opinion-item">
        <div class="opinion-box">
            <!-- 左侧内容：图标和舆情总数 -->
            <div class="opinion-content">
              <img :src="logoUrl4" class="opinion-image" />
              <div class="opinion-text">
                <h3>负面舆情
                  <a-tooltip title="开始日期到结束日期的发言人数">
                      <QuestionCircleOutlined style="color: #939699; margin-left: 5px" />
                  </a-tooltip>
                </h3>
                <p>{{ publicOpinionData.sumNegative }}个</p>
              </div>
            </div>

            <!-- 右侧内容：日环比和周环比 -->
            <a-row :gutter="[30, 8]" type="flex" justify="space-between" class="opinion-metrics">
              <a-col :span="10" class="metric">
                <div class="DoD">
                  日环比
                  <a-tooltip title="相对于结束日期前一日的增长或减少百分比">
                    <QuestionCircleOutlined style="color: #939699; margin-left: 5px" />
                  </a-tooltip>
                </div>

                <div class="DoD_per">
                  <span>{{ publicOpinionData.sumNegativeDayRatio }}</span>
                  <span>
                    <CaretUpOutlined v-if="publicOpinionData.sumNegativeDayFlag == '1'" style="color: #f5222d; margin-left: 5px;" />
                    <CaretDownOutlined v-else-if="publicOpinionData.sumNegativeDayFlag == '2'" style="color: #52c41a; margin-left: 5px;" />
                    <!-- <span v-else>{{ publicOpinionData.sumNegativeDayFlag }}</span> -->
                  </span>
                </div>
              </a-col>

              <a-col :span="10" class="metric">
                <div class="WoW">
                  周环比
                  <a-tooltip title="相对于结束日期前一周的增长或减少百分比">
                    <QuestionCircleOutlined style="color: #939699; margin-left: 5px" />
                  </a-tooltip>
                </div>

                <div class="WoW_per">
                  <span>{{ publicOpinionData.sumNegativeWeekRatio }}</span>
                  <span>
                    <CaretUpOutlined v-if="publicOpinionData.sumNegativeWeekFlag == '1'" style="color: #f5222d; margin-left: 5px;" />
                    <CaretDownOutlined v-else-if="publicOpinionData.sumNegativeWeekFlag == '2'" style="color: #52c41a; margin-left: 5px;" />
                    <!-- <span v-else>{{ publicOpinionData.sumNegativeWeekFlag }}</span> -->
                  </span>
                </div>
              </a-col>
            </a-row>
        </div>
      </div>

    </div>

    <div class="next_card">
      <a-row>
        <a-col :span="12">
          <div class="img_title">
            舆情渠道
            <a-button
              v-if="showTrendChart"
              class="chart-toggle-btn"
              @click="toggleShowAllData"
              style="float: right; margin-top: -2px;"
            >
              {{ showAllPieData ? '隐藏0%' : '显示全部' }}
            </a-button>
          </div>
          <div class="chart-container">
            <!-- <div v-show="showTrendChart" id="bing_tu" style="width: 40vw; height: 20vw;"></div> -->
             <NightingalePieChart
               v-if="showTrendChart"
               :chartData="opinon_data"
               :width="'40vw'"
               :height="'20vw'"
               :showAllData="showAllPieData"
             />
            <div v-show="!showTrendChart" class="chart-placeholder">请选择筛选条件</div>
          </div>

        </a-col>

        <a-col :span="12">
          <div class="img_title">舆情数量走势</div>
          <div class="chart-container">
            <!-- <div v-show="showTrendChart" id="opinion_qu" style="width: 40vw; height: 20vw;"></div> -->
            <LineChart
              v-if="showTrendChart"
              :chartData="{
                xAxisData: trendData.xAxis,
                seriesData: trendData.series1,
                // legendData: selectedChannels,
                legendData: value.map(item => item.value),   // ∵ 包含所有选中渠道（无论有没有数据）
                xAxisFormatter: (value) => dayjs(value).format('MM-DD')
              }"
              :colorList="color_list"
              :width="'40vw'"
              :height="'20vw'"
            />
            <div v-show="!showTrendChart" class="chart-placeholder">请选择筛选条件</div>
          </div>
          
        </a-col>
      </a-row>

      <a-row style="padding-bottom: 1vw;">
        <a-col :span="12">
          <div class="img_title">发言人数走势</div>
          <div class="chart-container">
            <!-- <div v-show="showTrendChart" id="remark_people" style="width: 40vw; height: 20vw;"></div> -->
            <LineChart
              v-if="showTrendChart"
              :chartData="{
                xAxisData: trendData.xAxis,
                seriesData: trendData.series2,
                // legendData: selectedChannels,
                legendData: value.map(item => item.value),   // ∵ 包含所有选中渠道（无论有没有数据）
                yAxisName: '人数'
              }"
              :colorList="color_list"
              :width="'40vw'"
              :height="'20vw'"
            />
            <div v-show="!showTrendChart" class="chart-placeholder">请选择筛选条件</div>
          </div>
          
        </a-col>
        <a-col :span="12">
          <div class="img_title">情感趋势</div>
          <div class="chart-container">
            <!-- <div v-show="showTrendChart" id="emotional_trends" style="width: 40vw; height: 20vw;"></div> -->
               <LineChart
                  v-if="showTrendChart"
                  :chartData="{
                    xAxisData: trendData.xAxis,
                    seriesData: trendData.series3,
                    legendData: ['正面', '中性', '负面']
                  }"
                  :colorList="color_list"
                  :width="'40vw'"
                  :height="'20vw'"
                />
            <div v-show="!showTrendChart" class="chart-placeholder">请选择筛选条件</div>
          </div>
          
        </a-col>
      </a-row>
    </div>

    <a-row>
      <a-col :span="12" class="best_card">
        <div class="third_card">
          <a-row style="padding-top: 1%; padding-left: 2%">
            <a-col :span="12">
              <div class="img_title">热点</div>
            </a-col>
          </a-row>
          <div class="chart-container"> 
            <div v-if="showTrendChart">
              <a-row style="margin-left: 4%; margin-top: 4%">
                <a-col :span="10">

                  <!-- 热点表格 -->
                  <a-table 
                    :columns="columns" 
                    :data-source="hotDataSource" 
                    :pagination="false" 
                    :bordered="false"
                    size="small"
                  />

                </a-col>
                <a-col :span="12" :offset="1">
                  <div class="bestsellers-container" id="hot">
                    <!-- <chart-word-cloud :options="state.chartOptions" /> -->
                     <WordCloudChart :chartData="hotChartData" :height="'200px'" :width="'300px'" />
                  </div>
                </a-col>
              </a-row>
            </div>
            <div v-else class="chart-placeholder">请选择筛选条件</div>
          </div>
          
        </div>
      </a-col>

      <a-col :span="12" class="best_card">
        <div class="third_card">
          <a-row style="padding-top: 1%; padding-left: 2%">
            <a-col :span="12">
              <div class="img_title">热门话题</div>
            </a-col>
          </a-row>
          <div class="chart-container">
            <div v-if="showTrendChart">
              <a-row style="margin-left: 4%; margin-top: 4%">
                <a-col :span="10">

                  <!-- 热门话题表格 -->
                  <a-table 
                    :columns="columns_1" 
                    :data-source="topicDataSource" 
                    :pagination="false" 
                    :bordered="false"
                    size="small"
                  />

                </a-col>
                <a-col :span="12" :offset="1">
                  <div class="bestsellers-container_1" id="trending-hot">
                    <!-- <chart-word-cloud :options="state_1.chartOptions" /> -->
                     <WordCloudChart :chartData="topicChartData" :height="'200px'" :width="'300px'" />
                  </div>

                </a-col>
              </a-row>
            </div>
            <div v-else class="chart-placeholder">请选择筛选条件</div>
          </div>
          
        </div>
      </a-col>

    </a-row>

  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, reactive, watch, nextTick, computed, h } from 'vue';
  import {QuestionCircleOutlined, CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons-vue';
  import * as echarts from 'echarts';
  import {BasicColumn } from '/@/components/Table';
  import 'echarts-wordcloud';
  import dayjs, { Dayjs } from 'dayjs';

  import { TreeSelect } from 'ant-design-vue';
  import { router } from '/@/router';
  import gameTitle from '../components/gameTitle.vue';

  import logoUrl1 from '@/assets/images/jinri.png';
  import logoUrl2 from '@/assets/images/taolun.png';
  import logoUrl3 from '@/assets/images/py.png';
  import logoUrl4 from '@/assets/images/ny.png';
  import { useUserStore } from '/@/store/modules/user';
  import { useGameStore } from '/@/store/modules/gameStore';
  import WordCloudChart from '../components/chart/WordCloudChart.vue';
  import NightingalePieChart from '../components/chart/NightingalePieChart.vue';
  import LineChart from '../components/chart/LineChart.vue';

  import { getCustomNameByPlanIdApi } from '/@/api/public-opinion-monitoring/today';
  import { getPublicOpinionDataApi, getHotDataApi, getRealTimePublicOpinionShow, getTrendingDataApi} from '/@/api/public-opinion-monitoring';

/** 1. 定义模板 ref */
const page = ref<HTMLElement | null>(null)

/** 2. 提供给 antd 的挂载点函数 */
const getPopupContainer = () => page.value!

// 时间选择器的值
const dateRange = ref<[Dayjs, Dayjs]>([dayjs().add(-7, 'day'), dayjs()]);
const rangePresets = ref([
  { label: '当天', value: [dayjs().add(-1, 'd'), dayjs()] },
  { label: '最近三天', value: [dayjs().add(-3, 'd'), dayjs()] },
  { label: '最近一周', value: [dayjs().add(-7, 'd'), dayjs()] },
  { label: '最近一个月', value: [dayjs().add(-1, 'month'), dayjs()] },
  { label: '最近三个月', value: [dayjs().add(-3, 'month'), dayjs()] },
  { label: '最近六个月', value: [dayjs().add(-6, 'month'), dayjs()] },
  { label: '最近一年', value: [dayjs().add(-1, 'year'), dayjs()] },
  { label: '最近两年', value: [dayjs().add(-2, 'year'), dayjs()] },
  { label: '最近三年', value: [dayjs().add(-3, 'year'), dayjs()] },
]);

// 时间选择器变化事件
const onRangeChange = (dates: Dayjs[], dateStrings: string[]) => {
  if (dates) {
    console.log('From: ', dates[0], ', to: ', dates[1]);
    console.log('From: ', dateStrings[0], ', to: ', dateStrings[1]);
    // 在这里添加调用 fetchPublicOpinionData 的代码
    fetchPublicOpinionData();
  } else {
    console.log('Clear');
    // 如果需要在清除时间范围时也刷新数据，可以在这里调用 fetchPublicOpinionData()
    // fetchPublicOpinionData();
  }
};

// const value = ref<string[]>([]);

// 修改 value 的类型定义
const value = ref<{ value: string; label: string }[]>([]);

watch(value, (selectedValues) => {
  if (!selectedValues) return;
  
  const selectedKeys = selectedValues.map(item => item.value);
  const isAllSelected = selectedKeys.includes('all');

  if (isAllSelected) {
    const allOptionsExceptAll = treeDataOptions.value
      .filter(opt => opt.value !== 'all')
      .map(opt => ({ value: opt.value, label: opt.label })); 

    if (selectedKeys.length === allOptionsExceptAll.length + 1) {
      value.value = []; // 清空选择
    } else {
      value.value = allOptionsExceptAll; // 选中所有
    }
  }
});

// 定义变量控制选择筛选条件后再显示图表
const showTrendChart = ref(false);

watch([dateRange, value], () => {
  if (
    dateRange.value.length === 2 && // 确保开始和结束日期都已选择
    value.value.length > 0           // 确保至少选择了一个展示维度
  ) {
    showTrendChart.value = true;
  } else {
    showTrendChart.value = false;
  }
});

// 舆情数据
const publicOpinionData = ref({
  publicOpinionCount: 0,
  publicOpinionDayRatio: '',
  publicOpinionWeekRatio: '',
  publicOpinionDayFlag:'',
  publicOpinionWeekFlag:'',

  participantsNumber: 0,
  participantsNumberDayRatio: '',
  participantsNumberWeekRatio: '',
  participantsNumberDayFlag:'',
  participantsNumberWeekFlag:'',

  sumPositive: 0,
  sumPositiveDayRatio: '',
  sumPositiveWeekRatio: '',
  sumPositiveDayFlag:'',
  sumPositiveWeekFlag:'',

  sumNegative: 0,
  sumNegativeDayRatio: '',
  sumNegativeWeekRatio: '',
  sumNegativeDayFlag:'',
  sumNegativeWeekFlag:''
});

  //定义热点表格列字段
  const columns: BasicColumn[] = [
    {
      title: '排名',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '热度',
      dataIndex: 'hot',
      key: 'hot',
    },
  ];

  //定义热点话题表格列字段
  const columns_1: BasicColumn[] = [
    {
      title: '排名',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '热度',
      dataIndex: 'hot',
      key: 'hot',
    },
  ];

// 设置热点表格的数据源
const hotDataSource = ref<string[]>([]);
const hotChartData  = ref<{ title: string; views: number }[]>([]);

// 设置热门话题表格的数据源
const topicDataSource = ref<string[]>([]);
const topicChartData = ref<{ title: string; views: number }[]>([]);

// 定义接口以明确类型
interface ChannelOption {
  value: string;
  label: string;
}

// 渠道列表数据
const treeData = ref<ChannelOption[]>([]);

const treeDataOptions = computed(() => {
  const options = treeData.value.map(item => ({
    value: item.value,
    label: item.label
  }));
  
  // 添加"选择全部"选项
  options.unshift({
    value: 'all',
    label: '选择全部'
  });
  
  return options;
});

  // 从 localStorage 获取 planId
const planId = ref<string>(localStorage.getItem('currentPlanId') || '')
console.log('planId在这里',planId)

// 折线图需要的数据
interface SeriesItem {
  name: string;
  data: number[];
}

// 在组件顶部定义趋势数据类型
interface TrendData {
  xAxis: string[];
  series1: SeriesItem[];
  series2: SeriesItem[];
  series3: SeriesItem[];
}

// 初始化时使用类型断言
const trendData = reactive<TrendData>({
  xAxis: [],
  series1: [],
  series2: [],
  series3: [],
});

const selectedChannels = ref<string[]>([]);

const maxTagPlaceholder = (omittedValues: any[]) => {
  return h('span', { class: 'ellipsis-tag' }, '...');
};

function onPlanUpdate({ planId: newId }) {
  planId.value = String(newId)
  localStorage.setItem('currentPlanId', planId.value)

  // 拉列表
  fetchChannelList().then(() => {
    // 列表拉完清空“已选渠道”，迫使 watch(value) 触发
    value.value = []
    // 或者：直接调用数据拉取
    // fetchRealTimePublicOpinionShow()
  })
}


watch(planId, val => {
  if (val) fetchChannelList()
})

// 获取渠道列表
// const fetchChannelList = async () => {
//   try {
//     if (!planId.value) {
//       console.warn('planId为空，无法获取渠道列表');
//       return;
//     }
//     const response = await getCustomNameByPlanIdApi({
//       planId: parseInt(planId.value),
//     });

//     // 检查 response 是否包含 results 数组
//     if (response && response.results && Array.isArray(response.results)) {
//       // 确保转换为 { value, label } 格式
//       treeData.value = response.results.map((item: string) => ({
//         value: item,
//         label: item,
//       }));

//     } else {
//       console.error('获取渠道列表返回数据格式不支持:', response);
//       treeData.value = [];
//     }
//   } catch (error) {
//     console.error('获取渠道列表出错:', error);
//     treeData.value = [];
//   }
// };

const fetchChannelList = async () => {
  try {
    const planId = ref(localStorage.getItem('currentPlanId') || '')
    // 更全面的空值检查（包括null/undefined/空字符串）
    if (planId.value === null || planId.value === undefined || planId.value === '') {
      console.warn('planId为空，跳过获取渠道列表');
      treeData.value = []; // 清空渠道列表
      value.value = [];    // 清空已选渠道
      return;
    }

    // 转换为数字并进行有效性校验
    const numericPlanId = Number(planId.value);
    
    // 检查是否为有效数字（包含0的情况）
    if (Number.isNaN(numericPlanId)) {
      console.warn(`非法的planId值: ${planId.value}，应为数字类型`);
      return;
    }

    const response = await getCustomNameByPlanIdApi({
      planId: numericPlanId  // 直接使用转换后的数字
    });

    // 处理响应数据
    if (response?.results?.length) {
      treeData.value = response.results.map((item: string) => ({
        value: item,
        label: item,
      }));
    } else {
      console.warn('获取渠道列表成功，但返回数据为空');
      treeData.value = []; // 确保清空之前的数据
    }
  } catch (error) {
    console.error('获取渠道列表失败:', error);
    treeData.value = []; // 出错时清空数据
  }
};

// 监听 planId 的变化
watch(planId, async (newVal, oldVal) => {
  // 清空数据的逻辑
  const resetData = () => {
    publicOpinionData.value = {
      publicOpinionCount: 0,
      publicOpinionDayRatio: '',
      publicOpinionWeekRatio: '',
      publicOpinionDayFlag:'',
      publicOpinionWeekFlag:'',

      participantsNumber: 0,
      participantsNumberDayRatio: '',
      participantsNumberWeekRatio: '',
      participantsNumberDayFlag:'',
      participantsNumberWeekFlag:'',

      sumPositive: 0,
      sumPositiveDayRatio: '',
      sumPositiveWeekRatio: '',
      sumPositiveDayFlag:'',
      sumPositiveWeekFlag:'',

      sumNegative: 0,
      sumNegativeDayRatio: '',
      sumNegativeWeekRatio: '',
      sumNegativeDayFlag:'',
      sumNegativeWeekFlag:''
    };
    treeData.value = [];
    value.value = [];
    showTrendChart.value = false;
  };

  if (newVal) {
    dateRange.value = [dayjs().add(-7, 'day'), dayjs()];
    await fetchChannelList();
    value.value = [];
    fetchPublicOpinionData();
  } else {
    resetData(); // 当 newVal 为空时直接重置数据
  }
}, { immediate: true }); // 添加 immediate 选项确保初始化时执行

// 获取舆情数据
const fetchPublicOpinionData = async () => {
  try {
    // 获取当前用户信息
    const userStore = useUserStore();

    // 添加类型保护
    if (!userStore.userInfo) {
      console.warn('userInfo 为空，无法获取舆情数据');
      return;
    }

    const userId = userStore.userInfo.id;

    // 从 localStorage 获取 planId
    // const planIdValue = parseInt(localStorage.getItem('currentPlanId') || '');
    const planIdValue = planId.value ? parseInt(planId.value) : null;

    if (!planIdValue) {
      console.warn('planId 为空或无效，无法获取舆情数据');
      publicOpinionData.value = {
        publicOpinionCount: 0,
        publicOpinionDayRatio: '',
        publicOpinionWeekRatio: '',
        publicOpinionDayFlag:'',
        publicOpinionWeekFlag:'',

        participantsNumber: 0,
        participantsNumberDayRatio: '',
        participantsNumberWeekRatio: '',
        participantsNumberDayFlag:'',
        participantsNumberWeekFlag:'',

        sumPositive: 0,
        sumPositiveDayRatio: '',
        sumPositiveWeekRatio: '',
        sumPositiveDayFlag:'',
        sumPositiveWeekFlag:'',

        sumNegative: 0,
        sumNegativeDayRatio: '',
        sumNegativeWeekRatio: '',
        sumNegativeDayFlag:'',
        sumNegativeWeekFlag:''
      };
      return;
    }

    // 构建参数对象
    let params = {
      planId: planIdValue,
      userId: userId,
      localDate: null as null | number, // 修改为 number 类型
      startTime: null as null | number,
      endTime: null as null | number,
    };

    // 检查是否选择了“当天”
    const startDate = dateRange.value[0];
    const endDate = dateRange.value[1];
    const isToday = endDate.isSame(startDate.add(1, 'day'), 'day');

    if (isToday) {
      // 如果是“当天”，只传递 localDate 时间戳
      params.localDate = Date.now(); // 直接传递时间戳
    } else {
      // 否则传递 startTime 和 endTime
      params.startTime = dateRange.value[0]?.valueOf() || null;
      params.endTime = dateRange.value[1]?.valueOf() || null;
    }

    // 打印参数对象用于调试
    console.log('获取舆情数据传递的参数:', params);

    const response = await getPublicOpinionDataApi(params);

    console.log('获取舆情数据返回的结果',response)

    if (response) {
      const results = response.results;
      publicOpinionData.value = {
        publicOpinionCount: results.publicOpinion,
        publicOpinionDayRatio: results.publicOpinionDayRatio,
        publicOpinionWeekRatio: results.publicOpinionWeekRatio,
        publicOpinionDayFlag: results.publicOpinionDayFlag,
        publicOpinionWeekFlag: results.publicOpinionWeekFlag,

        participantsNumber: results.participantsNumber,
        participantsNumberDayRatio: results.participantsNumberDayRatio,
        participantsNumberWeekRatio: results.participantsNumberWeekRatio,
        participantsNumberDayFlag: results.participantsNumberDayFlag,
        participantsNumberWeekFlag: results.participantsNumberWeekFlag,

        sumPositive: results.sumPositive,
        sumPositiveDayRatio: results.sumPositiveDayRatio,
        sumPositiveWeekRatio: results.sumPositiveWeekRatio,
        sumPositiveDayFlag: results.sumPositiveDayFlag,
        sumPositiveWeekFlag: results.sumPositiveWeekFlag,

        sumNegative: results.sumNegative,
        sumNegativeDayRatio: results.sumNegativeDayRatio,
        sumNegativeWeekRatio: results.sumNegativeWeekRatio,
        sumNegativeDayFlag: results.sumNegativeDayFlag,
        sumNegativeWeekFlag: results.sumNegativeWeekFlag
      };
    } else {
      console.error('获取舆情数据失败:', response.message);
    }
  } catch (error) {
    console.error('获取舆情数据出错:', error);
  }
};

// 获取热点数据
const fetchHotData = async () => {
  try {
    // 获取当前用户信息
    const userStore = useUserStore();

    // 添加类型保护
    if (!userStore.userInfo) {
      console.warn('userInfo 为空，无法获取热点数据');
      return;
    }

    const userId = userStore.userInfo.id;

    // 从 localStorage 获取 planId
    const planIdValue = parseInt(localStorage.getItem('currentPlanId') || '');
    if (!planIdValue) {
      console.warn('planId 为空，无法获取热点数据');
      return;
    }

    // 获取选中的渠道列表
    const selectedChannels = value.value.map(item => item.value);

    // 获取时间范围
    const startTime = dateRange.value[0]?.valueOf() || null;
    const endTime = dateRange.value[1]?.valueOf() || null;

    // 构建参数对象
    const params = {
      planId: planIdValue,
      userId: userId,
      selectedChannelList: selectedChannels,
      startTime: startTime,
      endTime: endTime,
    };

    // 打印参数对象用于调试
    console.log('获取热点数据传递的参数:', params);

    const response = await getHotDataApi(params);

    console.log('获取热点数据返回的结果:', response);

    if (response) {
      const results = response.results;
      // 只取前五条数据
      const topFiveResults = results.slice(0, 5);

      hotDataSource.value = topFiveResults.map((item: any, index: number) => ({
        key: (index + 1).toString(),
        id: (index + 1).toString(),
        name: item.token,
        hot: item.tokenCount.toString(),
      }));

      hotChartData.value = topFiveResults.map(item => ({
        title: item.token,
        views: item.tokenCount,
      }));
      console.log('热点词云图数据',hotChartData.value)

      await nextTick(); // 等待DOM更新
      // 重新渲染词云图
    
      
      console.log('热点词云图已更新')
    } else {
      console.error('获取热点数据失败:', response.message);
    }
  } catch (error) {
    console.error('获取热点数据出错:', error);
  }
};

// 获取热门话题数据
const fetchTrendingData = async () => {
  try {
    // 获取当前用户信息
    const userStore = useUserStore();

    // 添加类型保护
    if (!userStore.userInfo) {
      console.warn('userInfo 为空，无法获取热门话题数据');
      return;
    }

    const userId = userStore.userInfo.id;

    // 从 localStorage 获取 planId
    const planIdValue = parseInt(localStorage.getItem('currentPlanId') || '');
    if (!planIdValue) {
      console.warn('planId 为空，无法获取热门话题数据');
      return;
    }

    // 获取选中的渠道列表
    const selectedChannels = value.value.map(item => item.value);

    // 构建参数对象
    const params = {
      planId: planIdValue,
      userId: userId,
      selectedChannelList: selectedChannels,
      startTime: dateRange.value[0]?.valueOf() || null,
      endTime: dateRange.value[1]?.valueOf() || null,
    };

    // 打印参数对象用于调试
    console.log('获取热门话题数据传递的参数:', params);

    const response = await getTrendingDataApi(params);

    console.log('获取热门话题数据返回的结果:', response);

    if (response) {
      const results = response.results;
      // 只取前五条数据
      const topFiveResults = results.slice(0, 5);

      topicDataSource.value = topFiveResults.map((item: any, index: number) => ({
        key: (index + 1).toString(),
        id: (index + 1).toString(),
        name: item.token,
        hot: item.tokenCount.toString(),
      }));

      topicChartData.value = topFiveResults.map(item => ({
        title: item.token,
        views: item.tokenCount,
      }));
      console.log('热门话题词云图数据',topicChartData.value )

      await nextTick(); // 等待DOM更新
      // 重新渲染词云图
      
      console.log('热门话题词云图已更新');
    } else {
      console.error('获取热门话题数据失败:', response ? response.message : '未知错误');
    }
  } catch (error) {
    console.error('获取热门话题数据出错:', error);
  }
};


// 监听时间范围变化
watch(dateRange, () => {
  fetchHotData();
  fetchTrendingData();
});

// 监听渠道选择变化
watch(value, () => {
  fetchHotData();
  fetchTrendingData();
});

// 1. 保存图表实例
const pieChart = ref<echarts.ECharts | null>(null);
const opinionChart = ref<echarts.ECharts | null>(null);
const peopleChart = ref<echarts.ECharts | null>(null);
const emotionChart = ref<echarts.ECharts | null>(null);

// 2. 饼图数据类型
interface PieDataItem { name: string; value: number; }
const opinon_data = ref<PieDataItem[]>([]);

// 控制饼图是否显示全部数据（包括0%）
const showAllPieData = ref(false);

// 切换显示全部数据的函数
const toggleShowAllData = () => {
  showAllPieData.value = !showAllPieData.value;
};

// 2. 折线图数据类型
interface LineSeries { name: string; type: 'line'; stack?: string; data: number[]; }

const color_list = ref(['#ffa66e', '#4598e9', '#2c4273', '#f6e093', '#78b7c9', '#97b319', '#e58b7b']);


// 获取实时舆情数据
const fetchRealTimePublicOpinionShow = async () => {
  try {
    const userStore = useUserStore();
    if (!userStore.userInfo) return;

    const gameStore = useGameStore();
    const gameId = gameStore.gameInfo.gameId;

    if (!dateRange.value || dateRange.value.length < 2) {
      console.error('Invalid date range');
      return;
    }
    const selectedChannels = value.value.map(item => item.value);

    // 类型转换关键点
    const params = {
      userId: String(userStore.userInfo.id), // 显式转换为字符串
      gameId: gameId,
      startTime: dateRange.value[0]?.valueOf(),
      endTime: dateRange.value[1]?.valueOf(),
      channelName: selectedChannels
    };

    const response = await getRealTimePublicOpinionShow(params);

    // 检查 response 是否包含 results 且 results 是一个对象
    if (response.results && typeof response.results === 'object') {
      processChartData(response.results);
    }
  } catch (error) {
    // 获取实时舆情数据失败
  }
};

function processChartData(results: Record<string, any[]>) {
  // 1. 拿到用户在 a-select 中「真正」选中的渠道名数组
  const allSelected = value.value.map(item => item.value);

  // 2. 判断是否至少有一个渠道拿到非空数据
  const hasValidData = allSelected.some(ch =>
    Array.isArray(results[ch]) && results[ch]!.length > 0
  );

  // 3. 如果没数据，就销毁所有旧实例、清空数据后直接返回
  if (!hasValidData) {
    pieChart.value?.dispose();
    opinionChart.value?.dispose();
    peopleChart.value?.dispose();
    emotionChart.value?.dispose();

    // 清空饼图数据
    opinon_data.value = [];

    // 清空折线图数据
    trendData.xAxis = [];
    trendData.series1 = [];
    trendData.series2 = [];
    trendData.series3 = [];

    // 清空图例
    selectedChannels.value = [];

    return;
  }

  // 4. 过滤出那些确实有数据的渠道
  const channelList = allSelected.filter(ch =>
    Array.isArray(results[ch]) && results[ch]!.length > 0
  );

  // 5. 回写给外层的 selectedChannels（用于 legendData）
  selectedChannels.value = channelList;

  // 6. 构建「完整」的日期数组
  const startDay = dateRange.value[0].startOf('day');
  const endDay   = dateRange.value[1].startOf('day');
  const allDays: string[] = [];
  let cursor = startDay.clone();
  while (cursor.isBefore(endDay) || cursor.isSame(endDay, 'day')) {
    allDays.push(cursor.format('YYYY-MM-DD'));
    cursor = cursor.add(1, 'day');
  }
  trendData.xAxis = allDays;

  // 7. 构造饼图：包含所有选中渠道的总评论数（包括0值）
  const allSelectedChannels = value.value.map(item => item.value);
  opinon_data.value = allSelectedChannels.map(ch => ({
    name: ch,
    value: results[ch] ? results[ch].reduce((sum, itm) => sum + Number(itm.commentCount), 0) : 0
  }));

  // 8. 构造折线图「舆情数量走势」
  trendData.series1 = channelList.map(ch => ({
    name: ch,
    data: allDays.map(day => {
      const rec = results[ch]!.find(i => i.day === day);
      return rec ? Number(rec.commentCount) : 0;
    })
  }));

  // 9. 构造折线图「发言人数走势」
  trendData.series2 = channelList.map(ch => ({
    name: ch,
    data: allDays.map(day => {
      const rec = results[ch]!.find(i => i.day === day);
      return rec ? Number(rec.speakersCount) : 0;
    })
  }));

  // 10. 构造折线图「情感趋势」（正面/中性/负面）
  const emotionKeys = ['positiveCount', 'neutralCount', 'negativeCount'] as const;
  const emotionNames = ['正面', '中性', '负面'];
  trendData.series3 = emotionKeys.map((key, idx) => ({
    name: emotionNames[idx],
    data: allDays.map(day => {
      // 对所有渠道累计该情感值
      return channelList.reduce((sum, ch) => {
        const rec = results[ch]!.find(i => i.day === day);
        return sum + (rec ? Number(rec[key]) : 0);
      }, 0);
    })
  })) as SeriesItem[];
}


  // 页面挂载时获取渠道列表
onMounted(() => {
  fetchChannelList();
  fetchPublicOpinionData();
  fetchHotData(); // 调用热点数据接口
  fetchTrendingData(); // 调用热门话题数据接口
  // fetchRealTimePublicOpinionShow();

   // 当时间范围变化时，获取实时舆情数据
   watch(dateRange, () => {
    fetchRealTimePublicOpinionShow();
  });

  // 当选择展示维度变化时，获取实时舆情数据
  watch(value, () => {
    fetchHotData()
    fetchTrendingData()
    fetchRealTimePublicOpinionShow()
})

watch(planId, async (newVal) => {
  if (newVal) {
    // 重置日期范围为默认的一周
    dateRange.value = [dayjs().add(-7, 'day'), dayjs()];
    
    // 获取渠道列表
    await fetchChannelList();
    
    // 清空已选渠道
    value.value = [];

    if (!planId.value) {
    publicOpinionData.value = {
      publicOpinionCount: 0,
      publicOpinionDayRatio: '',
      publicOpinionWeekRatio: '',
      publicOpinionDayFlag:'',
      publicOpinionWeekFlag:'',

      participantsNumber: 0,
      participantsNumberDayRatio: '',
      participantsNumberWeekRatio: '',
      participantsNumberDayFlag:'',
      participantsNumberWeekFlag:'',

      sumPositive: 0,
      sumPositiveDayRatio: '',
      sumPositiveWeekRatio: '',
      sumPositiveDayFlag:'',
      sumPositiveWeekFlag:'',

      sumNegative: 0,
      sumNegativeDayRatio: '',
      sumNegativeWeekRatio: '',
      sumNegativeDayFlag:'',
      sumNegativeWeekFlag:''
    };
  }
    
    // 获取舆情数据
    fetchPublicOpinionData();
  }
});



});
</script>

<style scoped>
  .best_card {
    box-shadow:
      0px 0px 0px rgba(77, 85, 117, 0.05),
      0px 3px 7px rgba(77, 85, 117, 0.05),
      0px 5px 14px rgba(77, 85, 117, 0.04),
      0px 13px 18px rgba(77, 85, 117, 0.03),
      0px 20px 20px rgba(77, 85, 117, 0.01),
      0px 35px 30px rgba(77, 85, 117, 0);
    margin-bottom: 2%;
  }
  /* 页面整体布局 */
  .page-container {
    font-family: Arial, sans-serif;
    width: 100%;
    padding: 20px;
    background-color: #f9f9f9;
    height: 100vh; /* 设置固定高度 */
    overflow: auto;
    position: relative; /* 使子元素绝对定位基于此容器 */

  }

  /* 在原有样式基础上添加 */
.ant-picker-dropdown,
.ant-select-dropdown {
  position: absolute !important;
  z-index: 1000;
  transform-origin: 0 0;
}

.page-container {
  transform: translateZ(0); /* 创建新的层叠上下文 */
}
  .game-title {
    font-size: 20px;
    font-weight: bold;
    margin: 0;
  }
  .title_num{
    margin-left: 120px; 
    margin-top: -70px
  }

  .select_card {
    /* width: 98%; */
    /* height: 200%; */
    background-color: #ffffff;
    margin-top: 1vw;
    box-shadow:
      0px 0px 0px rgba(77, 85, 117, 0.05),
      0px 3px 7px rgba(77, 85, 117, 0.05),
      0px 5px 14px rgba(77, 85, 117, 0.04),
      0px 13px 18px rgba(77, 85, 117, 0.03),
      0px 20px 20px rgba(77, 85, 117, 0.01),
      0px 35px 30px rgba(77, 85, 117, 0);
  }
  .right_card {
    width: 120%;
    height: 1142px;
    background-color: #ffffff;
    margin-left: 6%;
    margin-top: 2.5%;
  }
  .bestsellers-container_1 {
    height: 18.56rem;
    background: #ffffff;

    #charts-content {
      /* 需要设置宽高后才会显示 */
      width: 80%;
      height: 80%;
    }
  }
  .bestsellers-container {
    height: 18.56rem;
    background: #ffffff;

    #charts-content {
      /* 需要设置宽高后才会显示 */
      width: 80%;
      height: 80%;
    }
  }


  /* 四个图标样式 */
  .icon {
    width: 5vw; 
    border-radius: 10px; 
    margin-left: 8%; 
    margin-top: 1vw
  }
  .title {
    font-size: 18px; 
    color: #626466;
    margin-left: 1vw;
  }
  .number {
    font-size: 25px; 
    font-weight: bold; 
    margin-top: 1%; 
    margin-left: 1vw
  }
  .unit {
    font-size: 15px; 
    font-weight: normal; 
    margin-left: 5vw; 
    margin-top: -55px; 
    color: #939699
  }

  /* 舆情数据样式设置 */
  .public-opinion {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  margin-top: 20px;
}

.opinion-item {
  flex: 1;
  margin: 0 10px;
}

.opinion-box {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #ddd;
  /* border: 1px solid red; */
  flex-direction: column; /* 改为垂直布局 */
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
/* 左侧内容：图标和文本 */
.opinion-content {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-start; /* 关键属性：强制左对齐 */
  margin-right: auto; /* 右侧自动填充 */
  margin-left: 10px;
}

.opinion-image {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  margin-right: 15px;
}

.opinion-text {
  text-align: left;
  flex: 1;
}

.opinion-text h3 {
  font-size: 16px;
  color: #666;
  margin-bottom: 5px;
}

.opinion-text p {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.opinion-box h3,
.opinion-box p {
  margin: 0;
}

/* 右侧内容：日环比和周环比 */
.opinion-metrics {
  display: flex;
  flex-direction: row;
  margin-top: 10px;
}

.metric{
  margin-left: -7vw;
  padding-left: 0 !important; /* 覆盖默认padding */
}

.DoD,.WoW  {
    font-size: 10px; 
    color: #a7a9ac;
    white-space: nowrap; /* 防止文字换行 */
    margin-left: 1vw;  /* 统一文字缩进 */
  }
  .DoD_per, .WoW_per {
    margin-left: 2vw;
    font-size: 15px; 
    font-weight: bold;
    white-space: nowrap; /* 防止文字换行 */
  }
  .DoD_img, .WoW_img {
    width: 10px; 
  }

  .kuai {
    width: 20px;
    height: 15px;
    margin-top: 12%;
  }
  .next_card {
    background-color: #ffffff;
    border-radius: 8px 8px 8px 8px;
    box-shadow:
      0px 0px 0px rgba(77, 85, 117, 0.05),
      0px 3px 7px rgba(77, 85, 117, 0.05),
      0px 5px 14px rgba(77, 85, 117, 0.04),
      0px 13px 18px rgba(77, 85, 117, 0.03),
      0px 20px 20px rgba(77, 85, 117, 0.01),
      0px 35px 30px rgba(77, 85, 117, 0);
  }
  .img_title {
    font-size: 18px; 
    font-weight: bold; 
    margin-left: 4%; 
    margin-top: 3%; 
    border-left: 3px solid #0893cf; 
    padding-left: 1%
  } 
  .opinion_card {
    background-color: #ffffff;
    height: 11vw;
    width: 20vw;
    border-radius: 8px 8px 8px 8px;
    margin: 0 10px;
    border: 1px solid #ddd;
    box-shadow:
      0px 0px 0px rgba(77, 85, 117, 0.05),
      0px 3px 7px rgba(77, 85, 117, 0.05),
      0px 5px 14px rgba(77, 85, 117, 0.04),
      0px 13px 18px rgba(77, 85, 117, 0.03),
      0px 20px 20px rgba(77, 85, 117, 0.01),
      0px 35px 30px rgba(77, 85, 117, 0);
  }
  .second_card {
    height: 6vw;
    margin-top: 1vw;
    margin-bottom: 6vw;
  }
  .top_card {
    width: 97%;
    height: 100px;
    background-color: #ffffff;
    margin-left: 1.5%;
    margin-top: 1%;
    border-radius: 8px 8px 8px 8px;
    box-shadow:
      0px 0px 0px rgba(77, 85, 117, 0.05),
      0px 3px 7px rgba(77, 85, 117, 0.05),
      0px 5px 14px rgba(77, 85, 117, 0.04),
      0px 13px 18px rgba(77, 85, 117, 0.03),
      0px 20px 20px rgba(77, 85, 117, 0.01),
      0px 35px 30px rgba(77, 85, 117, 0);
  }
  .search_input {
    width: 60%;
    height: 40px;
    border-radius: 20px 20px 20px 20px;
    background-color: #f1f3f5;
    border: 0px;
    margin-top: 5%;
    margin-left: 6%;
  }
  :deep(.ant-input.css-dev-only-do-not-override-1s8knlx) {
    background-color: #f1f3f5;
  }
  .third_card {
    margin-top: 2%; 
    background-color: #ffffff; 
    border-radius: 8px 8px 8px 8px;
    width: 99%;
  }

/* 未选择筛选条件时显示阴影 */
.chart-container {
  width: 40vw;
  height: 20vw;
  position: relative;
}

.chart-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #888;
  font-size: 16px;
  text-align: center;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 自定义按钮样式 */
.chart-toggle-btn {
  position: absolute;
  top: 8px; right: 12px;
  z-index: 20;
  padding: 4px 8px;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 2px;
  cursor: pointer;
}

.chart-toggle-btn:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

</style>
