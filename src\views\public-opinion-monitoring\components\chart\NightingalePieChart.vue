<template>
  <div
    ref="chartRef"
    :style="{ height, width }"
    class="chart-container"
  >
    <!-- 当没有有效数据时显示的空状态 -->
    <div v-if="showEmptyState" class="empty-state">
      <div class="empty-icon">📊</div>
      <div class="empty-text">暂无数据</div>
      <div class="empty-subtext">当前时间段内没有舆情数据</div>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, PropType, ref, Ref, watchEffect } from 'vue';
import { useECharts } from '@/hooks/web/useECharts';
export default defineComponent({
  name: '<PERSON><PERSON>ie<PERSON><PERSON>',
  props: {
    
    chartData: {
      type: Array,
      default: () => [],
    },
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: 'calc(100vh - 208px)',
    },
  },
  setup(props) {
    const chartRef = ref<HTMLDivElement | null>(null);
    const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>);
    const showEmptyState = ref(false);

    function initCharts() {
      // 过滤掉value为0的数据项
      const filteredData = props.chartData.filter((item: any) => item.value > 0);

      // 检查是否有有效数据
      if (filteredData.length === 0) {
        showEmptyState.value = true;
        return;
      } else {
        showEmptyState.value = false;
      }

      let option = {
        legend: {
          top: 'bottom',
          itemWidth: 12,
          itemHeight: 9,
          // 图例类型设置为可滚动
          type: 'scroll',
          // 图例布局为横向
          orient: 'horizontal',
          // 翻页按钮箭头大小
          pageIconSize: 12,
          // 翻页按钮与图例项之间的距离
          pageButtonItemGap: 6,
          // "1/2" 页码的颜色
          pageTextStyle: {
            color: '#999',
          },
          textStyle: {
            lineHeight: 20, // 增加行高
          },
          // 限制图例宽度
          width: '90%',
        },
        // tooltip: { trigger: 'item', formatter: '{b}: {d}%' },
        tooltip: { trigger: 'item' },
        series: {
          name: '舆情渠道',
          type: 'pie',
          radius: '55%',
          center: ['50%', '40%'],
          // roseType: 'area', // 南丁格尔图，使用面积比例
          roseType: 'radius',
          data: filteredData,
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}\n{d}%',
            avoidLabelOverlap: true,
          },
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      };
      setOptions(option);
      resize();
    }

    watchEffect(() => {
      props.chartData && initCharts();
    });

    return { chartRef, showEmptyState };
  },
});
</script>

<style scoped>
.chart-container {
  position: relative;
}

.empty-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #999;
  z-index: 10;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #666;
}

.empty-subtext {
  font-size: 14px;
  color: #999;
}
</style>
